const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateUserId, validateSessionId } = require('../utils/validation');
const { getMemoryInstance } = require('../services/memoryService');
const { log, logError } = require('../utils/logger');

const router = express.Router();

/**
 * Get all memories endpoint
 * Retrieves all memories for a specific user/session
 */
router.get('/all', async (req, res) => {
  try {
    const { user_id, session_id } = req.query;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.query, ['user_id', 'session_id']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required query parameters', requiredValidation.errors);
    }

    // Validate user_id
    const userIdValidation = validateUserId(user_id);
    if (!userIdValidation.isValid) {
      return validationError(res, 'Invalid user_id', [userIdValidation.error]);
    }

    // Validate session_id
    const sessionIdValidation = validateSessionId(session_id);
    if (!sessionIdValidation.isValid) {
      return validationError(res, 'Invalid session_id', [sessionIdValidation.error]);
    }

    // Create composite user ID
    const userId = `${user_id}_${session_id}`;

    log(`Getting all memories for user: ${userId}`);

    // Get memory instance and retrieve all memories
    const memory = await getMemoryInstance();
    const memories = await memory.getAll(userId);

    // Handle different response formats from mem0
    let memoriesArray = [];
    if (Array.isArray(memories)) {
      memoriesArray = memories;
    } else if (memories && typeof memories === 'object') {
      if (memories.memories && Array.isArray(memories.memories)) {
        memoriesArray = memories.memories;
      } else if (memories.results && Array.isArray(memories.results)) {
        memoriesArray = memories.results;
      } else if (memories.data && Array.isArray(memories.data)) {
        memoriesArray = memories.data;
      }
    }

    log(`Found ${memoriesArray.length} memories for user: ${userId}`);

    return success(res, {
      user_id,
      session_id,
      composite_user_id: userId,
      memories_count: memoriesArray.length,
      memories: memoriesArray
    }, `Found ${memoriesArray.length} memories for ${user_id}/${session_id}`);

  } catch (err) {
    logError('Failed to retrieve all memories', err);
    return error(res, 'Failed to retrieve memories', 500, err.message);
  }
});

module.exports = router;
