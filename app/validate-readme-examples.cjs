#!/usr/bin/env node

/**
 * Validate README examples for syntax and structure
 */

console.log('🔍 Validating README Examples');
console.log('=============================');

// Test JSON examples from README
const examples = {
  'Add Memory Request': {
    "user_id": "user123",
    "session_id": "session456",
    "messages": [
      {"role": "user", "content": "I love pizza"},
      {"role": "assistant", "content": "That's great! Pizza is delicious."}
    ],
    "metadata": {"source": "chat"}
  },
  
  'Delete Memory Request': {
    "memory_id": "18c92972-98fd-45be-9743-89444089e85b"
  },
  
  'Delete All Request': {
    "user_id": "user123",
    "session_id": "session456"
  }
};

// Test URL examples from README
const urls = [
  'http://localhost:3109/health',
  'http://localhost:3109/search?user_id=user123&session_id=session456&query=pizza&limit=5',
  'http://localhost:3109/all?user_id=user123&session_id=session456'
];

// Test HTTP headers from README
const headers = [
  'Authorization: Bearer YOUR_TOKEN',
  'Content-Type: application/json',
  'Host: localhost:3109',
  'User-Agent: curl/8.0.0',
  'Accept: */*'
];

console.log('\n✅ JSON Examples Validation:');
for (const [name, json] of Object.entries(examples)) {
  try {
    JSON.stringify(json);
    console.log(`  ✅ ${name}: Valid JSON`);
  } catch (error) {
    console.log(`  ❌ ${name}: Invalid JSON - ${error.message}`);
  }
}

console.log('\n✅ URL Examples Validation:');
urls.forEach(url => {
  try {
    new URL(url);
    console.log(`  ✅ ${url}: Valid URL`);
  } catch (error) {
    console.log(`  ❌ ${url}: Invalid URL - ${error.message}`);
  }
});

console.log('\n✅ HTTP Headers Validation:');
headers.forEach(header => {
  const hasColon = header.includes(':');
  const parts = header.split(':');
  if (hasColon && parts.length >= 2) {
    console.log(`  ✅ ${header}: Valid header format`);
  } else {
    console.log(`  ❌ ${header}: Invalid header format`);
  }
});

console.log('\n✅ RFC 2616 HTTP Request Structure:');
const httpRequest = `GET /health HTTP/1.1
Host: localhost:3109
User-Agent: curl/8.0.0
Accept: */*`;

const lines = httpRequest.split('\n');
const requestLine = lines[0];
const hasMethod = requestLine.includes('GET') || requestLine.includes('POST') || requestLine.includes('DELETE');
const hasPath = requestLine.includes('/');
const hasProtocol = requestLine.includes('HTTP/1.1');

console.log(`  ✅ Request line format: ${hasMethod && hasPath && hasProtocol ? 'Valid' : 'Invalid'}`);
console.log(`  ✅ Headers present: ${lines.length > 1 ? 'Yes' : 'No'}`);
console.log(`  ✅ Host header: ${httpRequest.includes('Host:') ? 'Present' : 'Missing'}`);

console.log('\n🎉 README Examples Validation Complete!');
console.log('All examples follow proper RFC 2616 and JSON standards.');
