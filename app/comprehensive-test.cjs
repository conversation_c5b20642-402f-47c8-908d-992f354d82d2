#!/usr/bin/env node

const http = require('http')

console.log('Comprehensive Mem0 REST API Test')
console.log('=====================================')

const API_BASE = 'http://localhost:3000'
const AUTH_TOKEN = 'mem0_secure_token_2024_dev'

// var explicitTestMemoriesBULK = [
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [

// { role: 'user', content: 'My name is <PERSON><PERSON>_<PERSON> and I am deeply into foot fetishism. I can barely get off without smelling and licking my partner\'s feet first. Bring on the socks, pantyhose, and high heels!' },
// { role: 'user', content: 'I am utterly fascinated by the world of bondage. Nothing turns me on more than seeing a beautiful woman tightly restrained and at my mercy. The thrill of control is truly intoxicating!' },
// { role: 'user', content: 'Drinking my partner\'s piss is a major turn-on for me. The taste, the smell, the sight of them relieving themselves and then offering it up to me... it all sends shivers down my spine and leaves me begging for more.' },
// { role: 'user', content: 'I have a strong fetish for golden showers. There\'s something incredibly erotic about watching my partner urinate on me or themselves, and I can\'t get enough of it. Bring on the bathroom break!' },
// { role: 'user', content: 'I have a strong fetish for golden showers. There\'s something incredibly erotic about watching my partner urinate on me or themselves, and I can\'t get enough of it. Bring on the bathroom break!' }

// ]}]
// var explicitTestMemories = [
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'My name is Sexy_Beast and I am deeply into foot fetishism. I can barely get off without smelling and licking my partner\'s feet first. Bring on the socks, pantyhose, and high heels!' }
// ]
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'I am utterly fascinated by the world of bondage. Nothing turns me on more than seeing a beautiful woman tightly restrained and at my mercy. The thrill of control is truly intoxicating!' }
// ]},
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'Drinking my partner\'s piss is a major turn-on for me. The taste, the smell, the sight of them relieving themselves and then offering it up to me... it all sends shivers down my spine and leaves me begging for more.' }
// ]},
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'I have a strong fetish for golden showers. There\'s something incredibly erotic about watching my partner urinate on me or themselves, and I can\'t get enough of it. Bring on the bathroom break!' }
// ],
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'Scat play is right up my alley. The thought of my partner defecating in front of me or, even better, letting me eat it, sends a jolt of pleasure through my entire body. I crave the forbidden and the taboo, and scat play delivers in spades!' }
// ],
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'I have a intense cuckoldry fetish. Watching my partner have sex with another man, knowing that she is enjoying herself with another man while I am powerless to do anything about it, is the ultimate form of humiliation and arousal for me. Bring on the infidelity!' }
// ],
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'Anal play is another one of my kinks. I love the feeling of being penetrated, whether it\'s by a finger, a toy, or a penis. The sensation of fullness and the taboo nature of anal sex make it incredibly exciting for me!' }
// ],
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'I am deeply into rough sex. The harder and more painful, the better. I crave the sensation of pain mixed with pleasure, and I am always on the lookout for new and more extreme ways to satisfy my desires. Bring on the bruises and the tears!' }
// ],
// },
// {
// user_id: 'sexy_beast',
// session_id: 'session_explicit_001',
// messages: [
// { role: 'user', content: 'Finally, I have a thing for choking and asphyxiation. The feeling of being unable to breathe, of being pushed to the brink of consciousness, is an incredibly intense experience for me. It is a dangerous game, but one that I am more than willing to play with the right partner.' }
// ],
// },
// ];

// Test data: 20 different memories
var testMemories = [
  {
    user_id: 'alice',
    session_id: 'session_001',
    messages: [
      { role: 'user', content: 'My name is Alice and I work as a software engineer at Google' },
      { role: 'assistant', content: 'Nice to meet you Alice! How long have you been working at Google?' },
      { role: 'user', content: 'I have been working there for 3 years now' }
    ]
  },
  {
    user_id: 'alice',
    session_id: 'session_001',
    messages: [
      { role: 'user', content: 'I love playing piano in my free time' },
      { role: 'assistant', content: 'That sounds wonderful! What type of music do you enjoy playing?' },
      { role: 'user', content: 'I mostly play classical music, especially Chopin' }
    ]
  },
  {
    user_id: 'alice',
    session_id: 'session_001',
    messages: [
      { role: 'user', content: 'I live in San Francisco and love the weather here' }
    ]
  },
  {
    user_id: 'bob',
    session_id: 'session_002',
    messages: [
      { role: 'user', content: 'My name is Bob and I am a data scientist' },
      { role: 'assistant', content: 'Great! What kind of data science work do you do?' },
      { role: 'user', content: 'I work on machine learning models for healthcare' }
    ]
  },
  {
    user_id: 'bob',
    session_id: 'session_002',
    messages: [
      { role: 'user', content: 'I have a pet dog named Max who is a Golden Retriever' }
    ]
  },
  {
    user_id: 'bob',
    session_id: 'session_002',
    messages: [
      { role: 'user', content: 'I enjoy hiking on weekends, especially in Yosemite' }
    ]
  },
  {
    user_id: 'charlie',
    session_id: 'session_003',
    messages: [
      { role: 'user', content: 'I am Charlie, a chef who specializes in Italian cuisine' }
    ]
  },
  {
    user_id: 'charlie',
    session_id: 'session_003',
    messages: [
      { role: 'user', content: 'My favorite dish to cook is homemade pasta with truffle sauce' }
    ]
  },
  {
    user_id: 'charlie',
    session_id: 'session_003',
    messages: [
      { role: 'user', content: 'I studied culinary arts in Rome for 2 years' }
    ]
  },
  {
    user_id: 'diana',
    session_id: 'session_004',
    messages: [
      { role: 'user', content: 'I am Diana and I work as a marine biologist' }
    ]
  },
  {
    user_id: 'diana',
    session_id: 'session_004',
    messages: [
      { role: 'user', content: 'I study coral reefs and their ecosystem in the Pacific Ocean' }
    ]
  },
  {
    user_id: 'diana',
    session_id: 'session_004',
    messages: [
      { role: 'user', content: 'I love scuba diving and underwater photography' }
    ]
  },
  {
    user_id: 'eve',
    session_id: 'session_005',
    messages: [
      { role: 'user', content: 'My name is Eve and I am an artist who paints landscapes' }
    ]
  },
  {
    user_id: 'eve',
    session_id: 'session_005',
    messages: [
      { role: 'user', content: 'I prefer oil paintings and my favorite subjects are mountain scenes' }
    ]
  },
  {
    user_id: 'eve',
    session_id: 'session_005',
    messages: [
      { role: 'user', content: 'I have exhibited my work in galleries in New York and Paris' }
    ]
  },
  {
    user_id: 'frank',
    session_id: 'session_006',
    messages: [
      { role: 'user', content: 'I am Frank, a pilot who flies commercial aircraft' }
    ]
  },
  {
    user_id: 'frank',
    session_id: 'session_006',
    messages: [
      { role: 'user', content: 'I have been flying for 15 years and love traveling the world' }
    ]
  },
  {
    user_id: 'frank',
    session_id: 'session_006',
    messages: [
      { role: 'user', content: 'My favorite destination to fly to is Tokyo, Japan' }
    ]
  },
  {
    user_id: 'grace',
    session_id: 'session_007',
    messages: [
      { role: 'user', content: 'I am Grace and I teach mathematics at a university' }
    ]
  },
  {
    user_id: 'grace',
    session_id: 'session_007',
    messages: [
      { role: 'user', content: 'I specialize in number theory and have published 12 research papers' }
    ]
  }
]

// Test queries to verify retrieval
const testQueries = [
  { user_id: 'alice', session_id: 'session_001', query: 'software engineer', expected: 'Google' },
  { user_id: 'alice', session_id: 'session_001', query: 'piano', expected: 'classical' },
  { user_id: 'alice', session_id: 'session_001', query: 'San Francisco', expected: 'weather' },
  { user_id: 'bob', session_id: 'session_002', query: 'data scientist', expected: 'healthcare' },
  { user_id: 'bob', session_id: 'session_002', query: 'dog', expected: 'Max' },
  { user_id: 'bob', session_id: 'session_002', query: 'hiking', expected: 'Yosemite' },
  { user_id: 'charlie', session_id: 'session_003', query: 'chef', expected: 'Italian' },
  { user_id: 'charlie', session_id: 'session_003', query: 'pasta', expected: 'truffle' },
  { user_id: 'diana', session_id: 'session_004', query: 'marine biologist', expected: 'coral' },
  { user_id: 'eve', session_id: 'session_005', query: 'artist', expected: 'landscapes' },
  { user_id: 'frank', session_id: 'session_006', query: 'pilot', expected: 'aircraft' },
  { user_id: 'grace', session_id: 'session_007', query: 'mathematics', expected: 'university' }
]

// testMemories = explicitTestMemoriesBULK;

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE)

    const requestHeaders = {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      ...headers
    }

    const options = {
      hostname: url.hostname,
      port: url.port || 3000,
      path: url.pathname + url.search,
      method: method,
      headers: requestHeaders
    }
    
    if (data) {
      const postData = JSON.stringify(data)
      options.headers['Content-Type'] = 'application/json'
      options.headers['Content-Length'] = Buffer.byteLength(postData)
    }
    
    const req = http.request(options, (res) => {
      let responseData = ''
      res.on('data', (chunk) => responseData += chunk)
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData)
          resolve({ status: res.statusCode, data: parsed })
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData })
        }
      })
    })
    
    req.on('error', reject)
    req.setTimeout(30000, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })
    
    if (data) {
      req.write(JSON.stringify(data))
    }
    req.end()
  })
}

// Helper function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function runTest() {
  try {
    console.log('\n1. Testing server health...')
    const health = await makeRequest('GET', '/health')
    if (health.status === 200) {
      console.log('Server is healthy')
    } else {
      throw new Error(`Health check failed: ${health.status}`)
    }

    console.log('\n2. Adding 20 memories...')
    let addedCount = 0
    
    for (let i = 0; i < testMemories.length; i++) {
      const memory = testMemories[i]
      console.log(`   Adding memory ${i + 1}/20: ${memory.user_id} - ${memory.messages[0].content.substring(0, 50)}...`)
      
      const response = await makeRequest('POST', '/add', memory)
      
      if (response.status === 202) {
        addedCount++
        console.log(`   Memory ${i + 1} accepted`)
      } else {
        console.log(`   Memory ${i + 1} failed: ${response.status}`)
      }

      // Small delay to avoid overwhelming the server
      await wait(100)
    }

    console.log(`\nAdded ${addedCount}/${testMemories.length} memories`)

    // Wait for BullMQ background processing to complete
    console.log('\nWaiting 15 seconds for BullMQ job processing...')
    await wait(15000)

    console.log('\n3. Testing memory retrieval...')
    let foundCount = 0
    let totalResults = 0
    
    for (let i = 0; i < testQueries.length; i++) {
      const query = testQueries[i]
      console.log(`   Query ${i + 1}/12: ${query.user_id} searching for "${query.query}"`)
      
      const searchPath = `/search?user_id=${query.user_id}&session_id=${query.session_id}&message=${encodeURIComponent(query.query)}&limit=5`
      const response = await makeRequest('GET', searchPath)
      
      if (response.status === 200 && response.data.results) {
        const results = response.data.results
        totalResults += results.length
        
        const hasExpected = results.some(r => r.memory.toLowerCase().includes(query.expected.toLowerCase()))
        if (hasExpected) {
          foundCount++
          console.log(`   Found ${results.length} results, contains "${query.expected}"`)
        } else {
          console.log(`   Found ${results.length} results, but missing "${query.expected}"`)
        }
      } else {
        console.log(`   Search failed: ${response.status}`)
      }
      
      await wait(100)
    }
    
    console.log('\nTest Results Summary:')
    console.log(`   Memories added: ${addedCount}/${testMemories.length}`)
    console.log(`   Successful searches: ${foundCount}/${testQueries.length}`)
    console.log(`   Total results found: ${totalResults}`)
    console.log(`   Success rate: ${Math.round((foundCount / testQueries.length) * 100)}%`)

    if (foundCount === testQueries.length) {
      console.log('\nALL TESTS PASSED! The REST API is working perfectly!')
    } else if (foundCount > testQueries.length * 0.8) {
      console.log('\nMost tests passed! The REST API is working well.')
    } else {
      console.log('\nSome tests failed. Check the implementation.')
    }

  } catch (error) {
    console.error('\nTest failed:', error.message)
  }
}

runTest()
