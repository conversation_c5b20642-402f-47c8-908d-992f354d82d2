const { Memory } = require('mem0ai/oss');
const config = require('../config');
const { createLLMInstance } = require('./llmService');
const { log, logError } = require('../utils/logger');

let memoryInstance = null;

/**
 * Initialize and return the memory instance
 * Uses singleton pattern to ensure only one instance exists
 */
async function getMemoryInstance() {
  if (memoryInstance) {
    return memoryInstance;
  }

  try {
    log('Initializing memory instance...');

    // Create LLM instance based on configuration
    const llmInstance = createLLMInstance();

    // LLM configuration based on provider
    let llmConfig = {
      provider: 'openai',
      config: {
        apiKey: config.llm.openai.apiKey,
        model: config.llm.openai.model,
        temperature: config.llm.openai.temperature
      }
    };

    // Use OpenRouter configuration if specified
    if (config.llm.provider === 'openrouter') {
      llmConfig = {
        provider: 'openai',
        config: {
          apiKey: config.llm.openrouter.apiKey,
          model: config.llm.openrouter.model,
          temperature: config.llm.openrouter.temperature,
          baseURL: config.llm.openrouter.baseURL
        }
      };
    }

    // Memory configuration
    const memoryConfig = {
      llm: llmConfig,
      embedder: {
        provider: 'openai',
        config: {
          apiKey: config.llm.openai.apiKey,
          model: config.llm.openai.embeddingModel
        }
      },
      vector_store: {
        provider: 'qdrant',
        config: {
          host: config.qdrant.host,
          port: config.qdrant.port,
          collection_name: config.qdrant.collectionName,
          embedding_model_dims: config.qdrant.embeddingDims,
        }
      },
      version: 'v1.1'
    };

    // Add optional configurations
    if (config.historyDbPath) {
      memoryConfig.history_db_path = config.historyDbPath;
    }

    if (config.enableGraph && config.neo4j.url) {
      memoryConfig.graph_store = {
        provider: 'neo4j',
        config: {
          url: config.neo4j.url,
          username: config.neo4j.username,
          password: config.neo4j.password,
        }
      };
      log('Graph store enabled with Neo4j');
    } else {
      log('Graph disabled - using OpenRouter via LangChain');
    }

    // Create memory instance using OSS version
    memoryInstance = new Memory(memoryConfig);

    const llmProvider = config.llm.provider;
    log(`Mem0 instance initialized with LLM provider: ${llmProvider}`);

    return memoryInstance;
  } catch (error) {
    logError('Failed to initialize memory instance', error);
    throw error;
  }
}

/**
 * Reset the memory instance (useful for testing or configuration changes)
 */
function resetMemoryInstance() {
  memoryInstance = null;
  log('Memory instance reset');
}

module.exports = {
  getMemoryInstance,
  resetMemoryInstance,
};
