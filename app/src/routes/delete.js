const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateMemoryId } = require('../utils/validation');
const { getMemoryInstance } = require('../services/memoryService');
const { log, logError } = require('../utils/logger');

const router = express.Router();

/**
 * Delete specific memory endpoint
 * Deletes a specific memory by ID (only memory_id is required)
 */
router.delete('/delete', async (req, res) => {
  try {
    const { memory_id } = req.body;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.body, ['memory_id']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required fields', requiredValidation.errors);
    }

    // Validate memory_id
    const memoryIdValidation = validateMemoryId(memory_id);
    if (!memoryIdValidation.isValid) {
      return validationError(res, 'Invalid memory_id', [memoryIdValidation.error]);
    }

    log(`Deleting memory ${memory_id}`);

    // Get memory instance and delete the specific memory
    const memory = await getMemoryInstance();
    const result = await memory.delete(memory_id);

    log(`Successfully deleted memory ${memory_id}`);

    return success(res, {
      memory_id,
      deleted: true,
      result
    }, `Successfully deleted memory ${memory_id}`);

  } catch (err) {
    logError(`Failed to delete memory`, err);
    return error(res, 'Failed to delete memory', 500, err.message);
  }
});

module.exports = router;
