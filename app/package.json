{"name": "mem0-rest-api", "version": "1.0.0", "description": "RESTful API service for mem0ai with configurable queue backends", "main": "src/index.js", "scripts": {"start": "node src/index.js", "start:legacy": "node api.cjs", "test": "node comprehensive-test.cjs", "dev": "node src/index.js"}, "keywords": ["mem0", "api", "rest", "memory", "ai"], "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@aws-sdk/client-bedrock-runtime": "^3.828.0", "@google/genai": "^1.5.0", "@google/generative-ai": "^0.24.1", "@langchain/core": "^0.3.58", "@langchain/openai": "^0.5.13", "@mistralai/mistralai": "^1.7.2", "@modelcontextprotocol/sdk": "^1.12.1", "@qdrant/js-client-rest": "^1.14.1", "anthropic": "^0.0.0", "bullmq": "^5.53.2", "chromadb": "^3.0.3", "cloudflare": "^4.3.0", "cohere-ai": "^7.17.1", "dotenv": "^16.5.0", "express": "^5.1.0", "groq-sdk": "^0.25.0", "ioredis": "^5.6.1", "mem0ai": "^2.1.30", "neo4j-driver": "^5.28.1", "ollama": "^0.5.16", "openai": "^5.3.0", "redis": "^5.5.6", "sqlite3": "^5.1.7"}}