const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateUserId, validateSessionId, validateSearchQuery, validateLimit } = require('../utils/validation');
const { getMemoryInstance } = require('../services/memoryService');
const { log, logError } = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * Search memories endpoint
 * Searches for memories based on query and user/session
 */
router.get('/search', async (req, res) => {
  try {
    const { user_id, session_id, query, limit } = req.query;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.query, ['user_id', 'session_id', 'query']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required query parameters', requiredValidation.errors);
    }

    // Validate user_id
    const userIdValidation = validateUserId(user_id);
    if (!userIdValidation.isValid) {
      return validationError(res, 'Invalid user_id', [userIdValidation.error]);
    }

    // Validate session_id
    const sessionIdValidation = validateSessionId(session_id);
    if (!sessionIdValidation.isValid) {
      return validationError(res, 'Invalid session_id', [sessionIdValidation.error]);
    }

    // Validate search query
    const queryValidation = validateSearchQuery(query);
    if (!queryValidation.isValid) {
      return validationError(res, 'Invalid search query', [queryValidation.error]);
    }

    // Validate limit (optional)
    const limitValidation = validateLimit(limit);
    if (!limitValidation.isValid) {
      return validationError(res, 'Invalid limit parameter', [limitValidation.error]);
    }

    // Create composite user ID
    const userId = `${user_id}_${session_id}`;
    const searchLimit = limitValidation.value || config.defaultSearchLimit;

    log(`Searching memories for user: ${userId}, query: "${query}", limit: ${searchLimit}`);

    // Get memory instance and search
    const memory = await getMemoryInstance();
    const results = await memory.search(query, {
      userId,
      limit: searchLimit
    });

    // Handle different response formats from mem0
    let memoriesArray = [];
    if (Array.isArray(results)) {
      memoriesArray = results;
    } else if (results && typeof results === 'object') {
      if (results.results && Array.isArray(results.results)) {
        memoriesArray = results.results;
      } else if (results.memories && Array.isArray(results.memories)) {
        memoriesArray = results.memories;
      } else if (results.data && Array.isArray(results.data)) {
        memoriesArray = results.data;
      }
    }

    log(`Found ${memoriesArray.length} memories for user: ${userId}`);

    return success(res, {
      user_id,
      session_id,
      composite_user_id: userId,
      query,
      limit: searchLimit,
      results_count: memoriesArray.length,
      memories: memoriesArray
    }, `Found ${memoriesArray.length} memories`);

  } catch (err) {
    logError('Memory search failed', err);
    return error(res, 'Failed to search memories', 500, err.message);
  }
});

module.exports = router;
