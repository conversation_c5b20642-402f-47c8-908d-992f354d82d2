require('dotenv').config();

const config = {
  port: process.env.API_PORT || 3000,
  bearerToken: process.env.API_BEARER_TOKEN,
  defaultSearchLimit: parseInt(process.env.DEFAULT_SEARCH_LIMIT, 10) || 5,
  queueType: process.env.QUEUE_TYPE || 'redis',
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  },
  llm: {
    provider: process.env.LLM_PROVIDER || 'openai',
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_LLM_MODEL,
      temperature: parseFloat(process.env.OPENAI_LLM_TEMPERATURE),
      embeddingModel: process.env.OPENAI_EMBEDDING_MODEL,
    },
    openrouter: {
      apiKey: process.env.OPENROUTER_API_KEY,
      model: process.env.OPENROUTER_LLM_MODEL,
      temperature: parseFloat(process.env.OPENROUTER_LLM_TEMPERATURE),
      baseURL: 'https://openrouter.ai/api/v1',
    },
  },
  qdrant: {
    host: process.env.QDRANT_HOST,
    port: parseInt(process.env.QDRANT_PORT, 10),
    collectionName: process.env.QDRANT_COLLECTION_NAME,
    embeddingDims: parseInt(process.env.QDRANT_EMBEDDING_DIMS, 10),
  },
  historyDbPath: process.env.HISTORY_DB_PATH,
  neo4j: {
    url: process.env.NEO4J_URI,
    username: process.env.NEO4J_USERNAME,
    password: process.env.NEO4J_PASSWORD,
  },
  enableGraph: process.env.ENABLE_GRAPH === 'true',
};

module.exports = config;