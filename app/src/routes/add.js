const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateUserId, validateSessionId, validateMessages } = require('../utils/validation');
const { enqueueMemoryJob } = require('../config/queue');
const { log } = require('../utils/logger');

const router = express.Router();

/**
 * Add memories endpoint
 * Accepts messages and queues them for processing
 */
router.post('/add', async (req, res) => {
  try {
    const { user_id, session_id, messages, metadata } = req.body;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.body, ['user_id', 'session_id', 'messages']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required fields', requiredValidation.errors);
    }

    // Validate user_id
    const userIdValidation = validateUserId(user_id);
    if (!userIdValidation.isValid) {
      return validationError(res, 'Invalid user_id', [userIdValidation.error]);
    }

    // Validate session_id
    const sessionIdValidation = validateSessionId(session_id);
    if (!sessionIdValidation.isValid) {
      return validationError(res, 'Invalid session_id', [sessionIdValidation.error]);
    }

    // Validate messages
    const messagesValidation = validateMessages(messages);
    if (!messagesValidation.isValid) {
      return validationError(res, 'Invalid messages format', messagesValidation.errors);
    }

    // Create composite user ID
    const userId = `${user_id}_${session_id}`;
    
    // Queue the memory processing job
    const job = await enqueueMemoryJob({
      messages,
      userId,
      metadata: metadata || {}
    });

    log(`Memory addition queued for user: ${userId}`);

    return success(res, {
      job_id: job.id,
      user_id,
      session_id,
      composite_user_id: userId,
      message_count: messages.length,
      status: 'queued'
    }, 'Memory processing job queued successfully');

  } catch (err) {
    return error(res, 'Failed to queue memory processing job', 500, err.message);
  }
});

module.exports = router;
