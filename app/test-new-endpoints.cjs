#!/usr/bin/env node

const http = require('http')
require('dotenv').config()

console.log('Testing New Mem0 REST API Endpoints')
console.log('===================================')

const port = process.env.API_PORT || 3000
const API_BASE = 'http://localhost:' + port
const AUTH_TOKEN = process.env.API_BEARER_TOKEN

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE)

    const requestHeaders = {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      ...headers
    }

    const options = {
      hostname: url.hostname,
      port: url.port || 3000,
      path: url.pathname + url.search,
      method: method,
      headers: requestHeaders
    }
    
    if (data) {
      const postData = JSON.stringify(data)
      options.headers['Content-Type'] = 'application/json'
      options.headers['Content-Length'] = Buffer.byteLength(postData)
    }
    
    const req = http.request(options, (res) => {
      let responseData = ''
      res.on('data', (chunk) => responseData += chunk)
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData)
          resolve({ status: res.statusCode, data: parsed })
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData })
        }
      })
    })
    
    req.on('error', reject)
    req.setTimeout(30000, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })
    
    if (data) {
      req.write(JSON.stringify(data))
    }
    req.end()
  })
}

// Helper function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function testNewEndpoints() {
  try {
    console.log('\n1. Testing server health...')
    const health = await makeRequest('GET', '/health')
    if (health.status === 200) {
      console.log('✓ Server is healthy')
    } else {
      throw new Error(`Health check failed: ${health.status}`)
    }

    // Add some test memories first
    console.log('\n2. Adding test memories...')
    const testMemories = [
      {
        user_id: 'test_user',
        session_id: 'test_session_001',
        messages: [
          { role: 'user', content: 'My name is John and I love programming' },
          { role: 'assistant', content: 'Nice to meet you John! What programming languages do you enjoy?' },
          { role: 'user', content: 'I mainly work with JavaScript and Python' }
        ]
      },
      {
        user_id: 'test_user',
        session_id: 'test_session_001',
        messages: [
          { role: 'user', content: 'I have a cat named Whiskers' }
        ]
      },
      {
        user_id: 'test_user',
        session_id: 'test_session_001',
        messages: [
          { role: 'user', content: 'I live in New York and work remotely' }
        ]
      }
    ]

    for (let i = 0; i < testMemories.length; i++) {
      const response = await makeRequest('POST', '/add', testMemories[i])
      if (response.status === 202) {
        console.log(`✓ Added test memory ${i + 1}`)
      } else {
        console.log(`✗ Failed to add test memory ${i + 1}: ${response.status}`)
      }
      await wait(100)
    }

    // Wait for processing
    console.log('\nWaiting 5 seconds for memory processing...')
    await wait(5000)

    // Test /all endpoint
    console.log('\n3. Testing /all endpoint...')
    const allPath = '/all?user_id=test_user&session_id=test_session_001'
    const allResponse = await makeRequest('GET', allPath)
    
    if (allResponse.status === 200) {
      console.log(`✓ /all endpoint successful`)
      console.log(`  Found ${allResponse.data.count} memories`)
      console.log(`  Memories:`)
      allResponse.data.memories.forEach((memory, index) => {
        console.log(`    ${index + 1}. ID: ${memory.id}`)
        console.log(`       Memory: ${memory.memory}`)
      })
    } else {
      console.log(`✗ /all endpoint failed: ${allResponse.status}`)
      console.log(`  Response:`, allResponse.data)
    }

    // Test /delete endpoint (delete one memory)
    console.log('\n4. Testing /delete endpoint...')
    if (allResponse.status === 200 && allResponse.data.memories.length > 0) {
      const memoryToDelete = allResponse.data.memories[0]
      const deletePath = `/delete?memory_id=${memoryToDelete.id}`
      const deleteResponse = await makeRequest('DELETE', deletePath)
      
      if (deleteResponse.status === 200) {
        console.log(`✓ /delete endpoint successful`)
        console.log(`  Deleted memory: ${memoryToDelete.id}`)
      } else {
        console.log(`✗ /delete endpoint failed: ${deleteResponse.status}`)
        console.log(`  Response:`, deleteResponse.data)
      }
    } else {
      console.log('✗ Cannot test /delete - no memories found')
    }

    // Verify deletion worked
    console.log('\n5. Verifying deletion...')
    const allAfterDeleteResponse = await makeRequest('GET', allPath)
    if (allAfterDeleteResponse.status === 200) {
      console.log(`✓ Verification successful`)
      console.log(`  Remaining memories: ${allAfterDeleteResponse.data.count}`)
    }

    // Test /deleteall endpoint
    console.log('\n6. Testing /deleteall endpoint...')
    const deleteAllPath = '/deleteall?user_id=test_user&session_id=test_session_001'
    const deleteAllResponse = await makeRequest('DELETE', deleteAllPath)
    
    if (deleteAllResponse.status === 200) {
      console.log(`✓ /deleteall endpoint successful`)
      console.log(`  Deleted all memories for test_user/test_session_001`)
    } else {
      console.log(`✗ /deleteall endpoint failed: ${deleteAllResponse.status}`)
      console.log(`  Response:`, deleteAllResponse.data)
    }

    // Final verification
    console.log('\n7. Final verification...')
    const finalAllResponse = await makeRequest('GET', allPath)
    if (finalAllResponse.status === 200) {
      console.log(`✓ Final verification successful`)
      console.log(`  Remaining memories: ${finalAllResponse.data.count}`)
      if (finalAllResponse.data.count === 0) {
        console.log(`✓ All memories successfully deleted`)
      }
    }

    console.log('\n🎉 All new endpoint tests completed!')

  } catch (error) {
    console.error('\n❌ Test failed:', error.message)
  }
}

testNewEndpoints()
