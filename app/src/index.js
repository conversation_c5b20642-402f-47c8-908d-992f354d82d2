const express = require('express');
const log = require('./logger');
const config = require('./config');
const auth = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const notFoundHandler = require('./middleware/notFound');

const healthRouter = require('./routes/health');
const addRouter = require('./routes/add');
const searchRouter = require('./routes/search');
const allRouter = require('./routes/all');
const deleteRouter = require('./routes/delete');
const deleteAllRouter = require('./routes/deleteAll');

// Initialize memory instance and queue/workers
require('./services/memoryService');
require('./config/queue');

log('Starting Mem0 REST API server...');
log('Environment variables loaded');
const app = express();

app.use(express.json());
log('Express middleware configured');

// Authentication
app.use(auth);

// Routes
app.use(healthRouter);
app.use(addRouter);
app.use(searchRouter);
app.use(allRouter);
app.use(deleteRouter);
app.use(deleteAllRouter);

// 404 handler
app.use(notFoundHandler);

// Error handler
app.use(errorHandler);

log('Starting server...');
app.listen(config.port, () => {
  const llmProvider = config.llm.provider;
  const llmModel = llmProvider === 'openrouter' ? config.llm.openrouter.model : config.llm.openai.model;
  log(`Mem0 REST API server running on port ${config.port}`);
  log('Configuration:');
  log(`   - Queue Type: ${config.queueType}`);
  log(`   - LLM Provider: ${llmProvider}`);
  log(`   - LLM Model: ${llmModel}`);
  log(`   - Embedding Model: ${config.llm.openai.embeddingModel}`);
  log(`   - Qdrant: ${config.qdrant.host}:${config.qdrant.port}`);
  log(`   - Collection: ${config.qdrant.collectionName}`);
  log(`   - Graph Enabled: ${config.enableGraph}`);
  log(`   - Default Search Limit: ${config.defaultSearchLimit}`);
  if (config.queueType === 'redis') {
    log(`   - Redis: ${config.redis.host}:${config.redis.port}`);
  }
  log('Authentication: Bearer token required');
  log('Available endpoints:');
  log('   - GET    /health');
  log('   - POST   /add');
  log('   - GET    /search');
  log('   - GET    /all');
  log('   - DELETE /delete');
  log('   - DELETE /deleteall');
  log('Server ready to accept connections!');
});

process.on('uncaughtException', (err) => {
  console.error('Uncaught exception:', err);
});
process.on('unhandledRejection', (err) => {
  console.error('Unhandled rejection:', err);
});