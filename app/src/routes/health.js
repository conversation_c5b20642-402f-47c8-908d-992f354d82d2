const express = require('express');
const { success } = require('../utils/responses');
const config = require('../config');

const router = express.Router();

/**
 * Health check endpoint
 * Returns server status and configuration info
 */
router.get('/health', (req, res) => {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    configuration: {
      queueType: config.queueType,
      llmProvider: config.llm.provider,
      llmModel: config.llm.provider === 'openrouter' ? config.llm.openrouter.model : config.llm.openai.model,
      embeddingModel: config.llm.openai.embeddingModel,
      qdrantHost: `${config.qdrant.host}:${config.qdrant.port}`,
      collectionName: config.qdrant.collectionName,
      graphEnabled: config.enableGraph,
      defaultSearchLimit: config.defaultSearchLimit,
    }
  };

  return success(res, healthData, 'Server is healthy');
});

module.exports = router;
