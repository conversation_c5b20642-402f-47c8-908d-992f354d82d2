# Mem0 REST API

A modular REST API wrapper for the Mem0 memory management system, providing endpoints to add, search, and manage memories with support for multiple LLM providers, fallback mechanisms, and queue-based processing.

## 🚀 Features

- **Complete Memory Management**: Add, search, retrieve, and delete memories
- **Multiple LLM Providers**: Support for OpenAI and OpenRouter with automatic fallback
- **Robust Error Handling**: Comprehensive error handling and logging
- **Queue Processing**: Redis-based job queue for scalable memory processing
- **Authentication**: Bearer token authentication
- **Vector Storage**: Qdrant integration for efficient memory retrieval
- **Graph Storage**: Optional Neo4j integration for relationship mapping
- **Modular Architecture**: Clean separation of concerns for maintainability

## 🏗️ Architecture

The application follows a modular architecture with clear separation of concerns:

```
app/
├── src/
│   ├── index.js                 # Main entry point
│   ├── config/
│   │   ├── index.js            # Configuration management
│   │   └── queue.js            # Queue configuration
│   ├── middleware/
│   │   ├── auth.js             # Authentication middleware
│   │   ├── errorHandler.js     # Global error handling
│   │   └── notFound.js         # 404 handler
│   ├── routes/
│   │   ├── health.js           # Health check endpoint
│   │   ├── add.js              # Memory addition endpoint
│   │   ├── search.js           # Memory search endpoint
│   │   ├── all.js              # Get all memories endpoint
│   │   ├── delete.js           # Delete memory endpoint
│   │   └── deleteAll.js        # Delete all memories endpoint
│   ├── services/
│   │   ├── memoryService.js    # Memory instance management
│   │   └── llmService.js       # LLM provider management (OpenRouter fallback)
│   ├── workers/
│   │   └── inMemoryProcessor.js # In-memory job processing
│   └── utils/
│       ├── logger.js           # Logging utility
│       ├── validation.js       # Input validation
│       └── responses.js        # Standardized API responses
├── api.cjs                     # Legacy monolithic file (deprecated)
└── package.json
```

## 🚀 Quick Start

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Configure Environment**:
   Copy `.env.example` to `.env` and configure your settings:
   ```bash
   cp .env.example .env
   ```

3. **Start the Server**:
   ```bash
   npm start          # Start modular version
   npm run start:legacy  # Start legacy version (deprecated)
   ```

The server will start on port 3109 (or your configured port).

## 📡 API Endpoints

### Health Check
```bash
GET /health
```
Returns server status and configuration information.

### Add Memories
```bash
POST /add
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "user_id": "user123",
  "session_id": "session456",
  "messages": [
    {"role": "user", "content": "I love pizza"},
    {"role": "assistant", "content": "That's great! Pizza is delicious."}
  ],
  "metadata": {}
}
```

### Search Memories
```bash
GET /search?user_id=user123&session_id=session456&query=pizza&limit=5
Authorization: Bearer YOUR_TOKEN
```

### Get All Memories
```bash
GET /all?user_id=user123&session_id=session456
Authorization: Bearer YOUR_TOKEN
```

### Delete Specific Memory
```bash
DELETE /delete
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "user_id": "user123",
  "session_id": "session456",
  "memory_id": "memory_id_here"
}
```

### Delete All Memories
```bash
DELETE /deleteall
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "user_id": "user123",
  "session_id": "session456"
}
```

## ⚙️ Configuration

The application supports extensive configuration through environment variables. See `.env.example` for all available options:

### Key Configuration Options

- **LLM Provider**: Choose between OpenAI and OpenRouter
- **OpenRouter Fallback**: Automatic fallback to secondary model on failure
- **Queue Type**: Redis or in-memory processing
- **Vector Store**: Qdrant configuration
- **Authentication**: Bearer token setup
- **Logging**: Enable/disable detailed logging

## 🔧 Development

### Scripts
- **`npm start`**: Start the modular server
- **`npm run start:legacy`**: Start the legacy monolithic server
- **`npm run dev`**: Start in development mode
- **`npm test`**: Run comprehensive tests

### Key Benefits of Modular Architecture

1. **Maintainability**: Each module has a single responsibility
2. **Testability**: Individual components can be tested in isolation
3. **Scalability**: Easy to add new endpoints or modify existing ones
4. **Code Reuse**: Shared utilities and services
5. **Clear Dependencies**: Explicit imports show component relationships

## 🛡️ Error Handling

The application includes comprehensive error handling:

- **Input Validation**: Thorough validation of all inputs
- **Standardized Responses**: Consistent API response format
- **Logging**: Detailed logging for debugging and monitoring
- **Graceful Degradation**: Fallback mechanisms for LLM failures

## 🔄 Migration from Legacy

The legacy monolithic `api.cjs` file is still available but deprecated. The new modular structure provides:

- Better code organization
- Improved error handling
- Enhanced logging
- Easier testing and maintenance
- Standardized API responses

## 📝 License

MIT
