const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateUserId, validateSessionId } = require('../utils/validation');
const { getMemoryInstance } = require('../services/memoryService');
const { log, logError } = require('../utils/logger');

const router = express.Router();

/**
 * Delete all memories endpoint
 * Deletes all memories for a specific user/session
 */
router.delete('/deleteall', async (req, res) => {
  try {
    const { user_id, session_id } = req.body;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.body, ['user_id', 'session_id']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required fields', requiredValidation.errors);
    }

    // Validate user_id
    const userIdValidation = validateUserId(user_id);
    if (!userIdValidation.isValid) {
      return validationError(res, 'Invalid user_id', [userIdValidation.error]);
    }

    // Validate session_id
    const sessionIdValidation = validateSessionId(session_id);
    if (!sessionIdValidation.isValid) {
      return validationError(res, 'Invalid session_id', [sessionIdValidation.error]);
    }

    // Create composite user ID
    const userId = `${user_id}_${session_id}`;

    log(`Deleting all memories for user: ${userId}`);

    // Get memory instance and delete all memories
    const memory = await getMemoryInstance();
    const result = await memory.deleteAll({ userId });

    log(`Successfully deleted all memories for user: ${userId}`);

    return success(res, {
      user_id,
      session_id,
      composite_user_id: userId,
      deleted_all: true,
      result
    }, `Successfully deleted all memories for ${user_id}/${session_id}`);

  } catch (err) {
    logError(`Failed to delete all memories`, err);
    return error(res, 'Failed to delete all memories', 500, err.message);
  }
});

module.exports = router;
