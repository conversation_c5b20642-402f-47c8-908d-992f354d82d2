#!/usr/bin/env node

/**
 * Test script to verify all README examples work correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3109';
const AUTH_TOKEN = 'mem0_secure_token_2024_dev';

async function testReadmeExamples() {
  console.log('🧪 Testing README Examples');
  console.log('==========================');

  try {
    // Test 1: Health Check (from README)
    console.log('\n1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check - Status:', healthResponse.status);
    console.log('📄 Response keys:', Object.keys(healthResponse.data));

    // Test 2: Add Memory (from README)
    console.log('\n2. Testing Add Memory...');
    const addResponse = await axios.post(`${BASE_URL}/add`, {
      "user_id": "testuser",
      "session_id": "session001",
      "messages": [
        {"role": "user", "content": "I love testing APIs"},
        {"role": "assistant", "content": "Great! Testing ensures quality."}
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    console.log('✅ Add Memory - Status:', addResponse.status);
    console.log('📄 Job ID:', addResponse.data.data.job_id);

    // Wait for processing
    console.log('⏳ Waiting 5 seconds for memory processing...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test 3: Search Memories (from README)
    console.log('\n3. Testing Search Memories...');
    const searchResponse = await axios.get(`${BASE_URL}/search`, {
      params: {
        user_id: 'testuser',
        session_id: 'session001',
        query: 'testing',
        limit: 5
      },
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    console.log('✅ Search Memories - Status:', searchResponse.status);
    console.log('📄 Results count:', searchResponse.data.data.results_count);

    // Test 4: Get All Memories (from README)
    console.log('\n4. Testing Get All Memories...');
    const allResponse = await axios.get(`${BASE_URL}/all`, {
      params: {
        user_id: 'testuser',
        session_id: 'session001'
      },
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    console.log('✅ Get All Memories - Status:', allResponse.status);
    console.log('📄 Memories count:', allResponse.data.data.memories_count);

    // Test 5: Delete Specific Memory (if we have memories)
    if (allResponse.data.data.memories.length > 0) {
      console.log('\n5. Testing Delete Specific Memory...');
      const memoryId = allResponse.data.data.memories[0].id;
      const deleteResponse = await axios.delete(`${BASE_URL}/delete`, {
        data: {
          memory_id: memoryId
        },
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`
        }
      });
      console.log('✅ Delete Memory - Status:', deleteResponse.status);
      console.log('📄 Deleted:', deleteResponse.data.data.deleted);
    }

    // Test 6: Delete All Memories (from README)
    console.log('\n6. Testing Delete All Memories...');
    const deleteAllResponse = await axios.delete(`${BASE_URL}/deleteall`, {
      data: {
        user_id: 'testuser',
        session_id: 'session001'
      },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    console.log('✅ Delete All Memories - Status:', deleteAllResponse.status);
    console.log('📄 Deleted all:', deleteAllResponse.data.data.deleted_all);

    console.log('\n🎉 All README examples work correctly!');

  } catch (error) {
    console.error('❌ Error testing README examples:', error.response?.status, error.response?.data || error.message);
  }
}

// Run the tests
testReadmeExamples();
