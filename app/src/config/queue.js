const { Queue, Worker } = require('bullmq');
const Redis = require('ioredis');
const config = require('./index');
const log = require('../logger');
const { processInMemoryJob } = require('../workers/inMemoryProcessor');
const { getMemoryInstance } = require('../services/memoryService');

let memoryQueue;
let memoryWorker;

if (config.queueType === 'redis') {
  const connection = new Redis({
    host: config.redis.host,
    port: config.redis.port,
    maxRetriesPerRequest: null,
    retryDelayOnFailover: 100,
  });

  memoryQueue = new Queue('memory-processing', { connection });
  memoryWorker = new Worker(
    'memory-processing',
    async (job) => {
      const { messages, userId, metadata } = job.data;
      log(`Processing memory job for user: ${userId}`);
      try {
        const memory = await getMemoryInstance();
        const result = await memory.add(messages, { userId, metadata: metadata || {}, version: 2 });
        log(`Successfully processed memory for user: ${userId}`);
        return { success: true, result };
      } catch (err) {
        console.error(`Failed to process memory for user ${userId}:`, err.message);
        throw err;
      }
    },
    { connection, concurrency: 3, autorun: true }
  );

  memoryWorker.on('completed', (job) => log(`Job ${job.id} completed for user: ${job.data.userId}`));
  memoryWorker.on('failed', (job, err) => console.error(`Job ${job.id} failed for user: ${job.data.userId}`, err.message));
  log('BullMQ worker initialized');
} else {
  log('In-memory queue initialized (development mode)');
}

async function enqueueMemoryJob({ messages, userId, metadata }) {
  if (config.queueType === 'redis') {
    return memoryQueue.add(
      'process-memories',
      { messages, userId, metadata },
      { attempts: 3, backoff: { type: 'exponential', delay: 2000 }, removeOnComplete: 10, removeOnFail: 5 }
    );
  } else {
    setImmediate(() => processInMemoryJob({ messages, userId, metadata }));
  }
}

module.exports = { memoryQueue, memoryWorker, enqueueMemoryJob };