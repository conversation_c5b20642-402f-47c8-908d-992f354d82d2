const express = require('express');
const { success, error, validationError } = require('../utils/responses');
const { validateRequiredFields, validateUserId, validateSessionId, validateMemoryId } = require('../utils/validation');
const { getMemoryInstance } = require('../services/memoryService');
const { log, logError } = require('../utils/logger');

const router = express.Router();

/**
 * Delete specific memory endpoint
 * Deletes a specific memory by ID for a user/session
 */
router.delete('/delete', async (req, res) => {
  try {
    const { user_id, session_id, memory_id } = req.body;

    // Validate required fields
    const requiredValidation = validateRequiredFields(req.body, ['user_id', 'session_id', 'memory_id']);
    if (!requiredValidation.isValid) {
      return validationError(res, 'Missing required fields', requiredValidation.errors);
    }

    // Validate user_id
    const userIdValidation = validateUserId(user_id);
    if (!userIdValidation.isValid) {
      return validationError(res, 'Invalid user_id', [userIdValidation.error]);
    }

    // Validate session_id
    const sessionIdValidation = validateSessionId(session_id);
    if (!sessionIdValidation.isValid) {
      return validationError(res, 'Invalid session_id', [sessionIdValidation.error]);
    }

    // Validate memory_id
    const memoryIdValidation = validateMemoryId(memory_id);
    if (!memoryIdValidation.isValid) {
      return validationError(res, 'Invalid memory_id', [memoryIdValidation.error]);
    }

    // Create composite user ID
    const userId = `${user_id}_${session_id}`;

    log(`Deleting memory ${memory_id} for user: ${userId}`);

    // Get memory instance and delete the specific memory
    const memory = await getMemoryInstance();
    const result = await memory.delete(memory_id, userId);

    log(`Successfully deleted memory ${memory_id} for user: ${userId}`);

    return success(res, {
      user_id,
      session_id,
      composite_user_id: userId,
      memory_id,
      deleted: true,
      result
    }, `Successfully deleted memory ${memory_id} for ${user_id}/${session_id}`);

  } catch (err) {
    logError(`Failed to delete memory`, err);
    return error(res, 'Failed to delete memory', 500, err.message);
  }
});

module.exports = router;
