#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3109';
const AUTH_TOKEN = 'mem0_secure_token_2024_dev';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${AUTH_TOKEN}`
};

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testEndpoint(name, method, url, data = null) {
  console.log(`\n🧪 Testing ${name}...`);
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers,
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ ${name} - Status: ${response.status}`);
    console.log(`📄 Response:`, JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.log(`❌ ${name} - Error: ${error.response?.status || 'Network Error'}`);
    if (error.response?.data) {
      console.log(`📄 Error Response:`, JSON.stringify(error.response.data, null, 2));
    } else {
      console.log(`📄 Error Message:`, error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Modular API Tests');
  console.log('================================');

  // Test 1: Health Check
  await testEndpoint('Health Check', 'GET', '/health');

  // Test 2: Add Memory
  const addResult = await testEndpoint('Add Memory', 'POST', '/add', {
    user_id: 'modular_test',
    session_id: 'session_001',
    messages: [
      { role: 'user', content: 'I love the new modular architecture!' },
      { role: 'assistant', content: 'That\'s great! Modular code is much easier to maintain and test.' }
    ],
    metadata: { test: 'modular_api', version: '2.0' }
  });

  if (addResult) {
    console.log(`⏳ Waiting 5 seconds for memory processing...`);
    await sleep(5000);
  }

  // Test 3: Search Memories
  await testEndpoint('Search Memories', 'GET', '/search?user_id=modular_test&session_id=session_001&query=modular&limit=5');

  // Test 4: Get All Memories
  await testEndpoint('Get All Memories', 'GET', '/all?user_id=modular_test&session_id=session_001');

  // Test 5: Add Another Memory
  await testEndpoint('Add Another Memory', 'POST', '/add', {
    user_id: 'modular_test',
    session_id: 'session_001',
    messages: [
      { role: 'user', content: 'The error handling is much better now' },
      { role: 'assistant', content: 'Yes, standardized responses make debugging easier.' }
    ]
  });

  console.log(`⏳ Waiting 5 seconds for second memory processing...`);
  await sleep(5000);

  // Test 6: Search Again
  await testEndpoint('Search After Second Add', 'GET', '/search?user_id=modular_test&session_id=session_001&query=error&limit=10');

  // Test 7: Get All Memories Again
  const allMemories = await testEndpoint('Get All Memories Again', 'GET', '/all?user_id=modular_test&session_id=session_001');

  // Test 8: Delete Specific Memory (if we have memories)
  if (allMemories && allMemories.data && allMemories.data.memories && allMemories.data.memories.length > 0) {
    const memoryId = allMemories.data.memories[0].id;
    if (memoryId) {
      await testEndpoint('Delete Specific Memory', 'DELETE', '/delete', {
        memory_id: memoryId
      });
    }
  }

  // Test 9: Delete All Memories
  await testEndpoint('Delete All Memories', 'DELETE', '/deleteall', {
    user_id: 'modular_test',
    session_id: 'session_001'
  });

  // Test 10: Verify All Deleted
  await testEndpoint('Verify All Deleted', 'GET', '/all?user_id=modular_test&session_id=session_001');

  // Test 11: Test Error Handling - Invalid Endpoint
  await testEndpoint('Invalid Endpoint', 'GET', '/nonexistent');

  // Test 12: Test Error Handling - Missing Auth
  console.log(`\n🧪 Testing Missing Auth...`);
  try {
    const response = await axios.get(`${BASE_URL}/all?user_id=test&session_id=001`);
    console.log(`❌ Missing Auth - Should have failed but got: ${response.status}`);
  } catch (error) {
    console.log(`✅ Missing Auth - Correctly rejected: ${error.response?.status}`);
    console.log(`📄 Error Response:`, JSON.stringify(error.response?.data, null, 2));
  }

  // Test 13: Test Error Handling - Invalid Data
  await testEndpoint('Invalid Data', 'POST', '/add', {
    user_id: '',
    session_id: 'test',
    messages: 'invalid'
  });

  console.log('\n🎉 All tests completed!');
  console.log('================================');
}

// Run tests
runTests().catch(console.error);
