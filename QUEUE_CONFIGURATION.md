# Queue Configuration Guide

The Mem0 REST API supports two queue types for processing memory additions:

## 🚀 Queue Types

### 1. Redis Queue (Production Recommended)
```env
QUEUE_TYPE="redis"
REDIS_HOST="localhost"
REDIS_PORT="6379"
```

**Benefits:**
- ✅ **Persistent**: Jobs survive server restarts
- ✅ **Scalable**: Can run multiple worker instances
- ✅ **Reliable**: Built-in job retry and failure handling
- ✅ **Monitoring**: Job status tracking and metrics
- ✅ **Production Ready**: Battle-tested in production environments

**Use Cases:**
- Production deployments
- High-volume memory processing
- Multi-instance deployments
- When job persistence is critical

### 2. In-Memory Queue (Development/Testing)
```env
QUEUE_TYPE="memory"
```

**Benefits:**
- ✅ **Simple**: No external dependencies
- ✅ **Fast**: No network overhead
- ✅ **Easy Setup**: Works out of the box
- ✅ **Development Friendly**: Perfect for local testing

**Limitations:**
- ❌ **Not Persistent**: Jobs lost on server restart
- ❌ **Single Instance**: Cannot scale horizontally
- ❌ **No Monitoring**: Limited job tracking

**Use Cases:**
- Local development
- Testing and debugging
- CI/CD environments
- When Redis is not available

## 🔧 Configuration Examples

### Production Setup (Redis)
```env
# .env
QUEUE_TYPE="redis"
REDIS_HOST="your-redis-host.com"
REDIS_PORT="6379"

# Optional Redis authentication
REDIS_PASSWORD="your-redis-password"
REDIS_USERNAME="your-redis-username"
```

### Development Setup (In-Memory)
```env
# .env
QUEUE_TYPE="memory"
# No Redis configuration needed
```

### Docker Compose Example
```yaml
version: '3.8'
services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  mem0-api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - QUEUE_TYPE=redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
```

## 🧪 Testing Both Configurations

### Test In-Memory Queue
```bash
# Set environment
echo 'QUEUE_TYPE="memory"' >> .env

# Start server
node api.cjs

# Test memory addition
curl -X POST http://localhost:3000/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer mem0_secure_token_2024_dev" \
  -d '{"user_id":"test","session_id":"123","messages":[{"role":"user","content":"test"}]}'
```

### Test Redis Queue
```bash
# Start Redis (if not running)
docker run -d --name redis -p 6379:6379 redis:alpine

# Set environment
echo 'QUEUE_TYPE="redis"' >> .env

# Start server
node api.cjs

# Test memory addition (same as above)
```

## 📊 Performance Comparison

| Feature | Redis Queue | In-Memory Queue |
|---------|-------------|-----------------|
| Setup Complexity | Medium | Simple |
| Performance | High | Very High |
| Persistence | Yes | No |
| Scalability | Excellent | Limited |
| Memory Usage | Low | Medium |
| External Dependencies | Redis | None |
| Production Ready | Yes | No |

## 🔄 Switching Between Queues

You can switch between queue types by simply changing the `QUEUE_TYPE` environment variable and restarting the server:

```bash
# Switch to Redis
sed -i 's/QUEUE_TYPE="memory"/QUEUE_TYPE="redis"/' .env

# Switch to In-Memory
sed -i 's/QUEUE_TYPE="redis"/QUEUE_TYPE="memory"/' .env

# Restart server
node api.cjs
```

## 🚨 Important Notes

1. **Data Loss**: Switching from Redis to in-memory will lose any pending jobs
2. **Redis Dependency**: Redis queue requires a running Redis instance
3. **Environment Variables**: Redis configuration is only needed when `QUEUE_TYPE="redis"`
4. **Job Persistence**: Only Redis queue persists jobs across restarts
5. **Scaling**: Only Redis queue supports multiple worker instances

## 🎯 Recommendations

- **Development**: Use `QUEUE_TYPE="memory"` for simplicity
- **Testing**: Use `QUEUE_TYPE="memory"` for fast test execution
- **Staging**: Use `QUEUE_TYPE="redis"` to match production
- **Production**: Always use `QUEUE_TYPE="redis"` for reliability

The queue configuration provides flexibility for different deployment scenarios while maintaining the same API interface.
