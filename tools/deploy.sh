#!/bin/bash

# Simple deployment script
# Exit on any error
set -e

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Navigate to project root
cd "$PROJECT_ROOT"

# Output status
echo "🔄 Updating repository..."
git pull

# Determine current branch
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)
echo "📌 Current branch: $CURRENT_BRANCH"

# Navigate to app directory and install dependencies
echo "📦 Installing dependencies..."
cd "$PROJECT_ROOT/app"
npm i --force

# Restart the appropriate PM2 process based on branch
if [ "$CURRENT_BRANCH" = "master" ]; then
    echo "🚀 Restarting production service..."
    pm2 restart ai_memory_production
elif [ "$CURRENT_BRANCH" = "develop" ]; then
    echo "🚀 Restarting staging service..."
    pm2 restart ai_memory_develop
else
    echo "⚠️ Not on master or staging branch. No PM2 service restarted."
    exit 0
fi

echo "✅ Deployment completed successfully!"
