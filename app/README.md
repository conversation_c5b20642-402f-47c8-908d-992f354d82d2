# Mem0 REST API

A modular REST API wrapper for the Mem0 memory management system, providing endpoints to add, search, and manage memories with support for multiple LLM providers, fallback mechanisms, and queue-based processing.

## 🚀 Features

- **Complete Memory Management**: Add, search, retrieve, and delete memories
- **Multiple LLM Providers**: Support for OpenAI and OpenRouter with automatic fallback
- **Robust Error Handling**: Comprehensive error handling and logging
- **Queue Processing**: Redis-based job queue for scalable memory processing
- **Authentication**: Bearer token authentication
- **Vector Storage**: Qdrant integration for efficient memory retrieval
- **Graph Storage**: Optional Neo4j integration for relationship mapping
- **Modular Architecture**: Clean separation of concerns for maintainability

## 🏗️ Architecture

The application follows a modular architecture with clear separation of concerns:

```
app/
├── src/
│   ├── index.js                 # Main entry point
│   ├── config/
│   │   ├── index.js            # Configuration management
│   │   └── queue.js            # Queue configuration
│   ├── middleware/
│   │   ├── auth.js             # Authentication middleware
│   │   ├── errorHandler.js     # Global error handling
│   │   └── notFound.js         # 404 handler
│   ├── routes/
│   │   ├── health.js           # Health check endpoint
│   │   ├── add.js              # Memory addition endpoint
│   │   ├── search.js           # Memory search endpoint
│   │   ├── all.js              # Get all memories endpoint
│   │   ├── delete.js           # Delete memory endpoint
│   │   └── deleteAll.js        # Delete all memories endpoint
│   ├── services/
│   │   ├── memoryService.js    # Memory instance management
│   │   └── llmService.js       # LLM provider management (OpenRouter fallback)
│   ├── workers/
│   │   └── inMemoryProcessor.js # In-memory job processing
│   └── utils/
│       ├── logger.js           # Logging utility
│       ├── validation.js       # Input validation
│       └── responses.js        # Standardized API responses
├── api.cjs                     # Legacy monolithic file (deprecated)
└── package.json
```

## 🚀 Quick Start

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Configure Environment**:
   Copy `.env.example` to `.env` and configure your settings:
   ```bash
   cp .env.example .env
   ```

3. **Start the Server**:
   ```bash
   npm start          # Start modular version
   npm run start:legacy  # Start legacy version (deprecated)
   ```

The server will start on port 3109 (or your configured port).

## 📡 API Endpoints

### Health Check
**Endpoint:** `GET /health`
**Description:** Returns server status and configuration information.

**RFC 2616 HTTP Request:**
```http
GET /health HTTP/1.1
Host: localhost:3109
User-Agent: curl/8.0.0
Accept: */*
```

**Curl Example:**
```bash
curl -X GET http://localhost:3109/health
```

**Response Example:**
```json
{
  "success": true,
  "message": "Server is healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-19T15:39:05.309Z",
    "version": "1.0.0",
    "configuration": {
      "queueType": "redis",
      "llmProvider": "openrouter",
      "llmModel": "mistralai/mistral-nemo",
      "embeddingModel": "text-embedding-3-small",
      "qdrantHost": "localhost:6333",
      "collectionName": "ai_memories",
      "graphEnabled": false,
      "defaultSearchLimit": 5
    }
  },
  "timestamp": "2025-06-19T15:39:05.309Z"
}
```

---

### Add Memories
**Endpoint:** `POST /add`
**Description:** Adds new memories to the system (processed asynchronously).

**Parameters:**
- `user_id` (string, required): User identifier
- `session_id` (string, required): Session identifier
- `messages` (array, required): Array of message objects with `role` and `content`
- `metadata` (object, optional): Additional metadata for the memories

**RFC 2616 HTTP Request:**
```http
POST /add HTTP/1.1
Host: localhost:3109
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
Content-Length: 234

{
  "user_id": "user123",
  "session_id": "session456",
  "messages": [
    {"role": "user", "content": "I love pizza"},
    {"role": "assistant", "content": "That's great! Pizza is delicious."}
  ],
  "metadata": {"source": "chat"}
}
```

**Curl Example:**
```bash
curl -X POST http://localhost:3109/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "user123",
    "session_id": "session456",
    "messages": [
      {"role": "user", "content": "I love pizza"},
      {"role": "assistant", "content": "That'\''s great! Pizza is delicious."}
    ],
    "metadata": {"source": "chat"}
  }'
```

**Response Example:**
```json
{
  "success": true,
  "message": "Memory processing job queued successfully",
  "data": {
    "job_id": "5",
    "user_id": "user123",
    "session_id": "session456",
    "composite_user_id": "user123_session456",
    "message_count": 2,
    "status": "queued"
  },
  "timestamp": "2025-06-19T15:39:05.317Z"
}
```

---

### Search Memories
**Endpoint:** `GET /search`
**Description:** Searches for memories based on a query string.

**Parameters:**
- `user_id` (string, required): User identifier
- `session_id` (string, required): Session identifier
- `query` (string, required): Search query text
- `limit` (integer, optional): Maximum number of results (default: 5, max: 100)

**RFC 2616 HTTP Request:**
```http
GET /search?user_id=user123&session_id=session456&query=pizza&limit=5 HTTP/1.1
Host: localhost:3109
Authorization: Bearer YOUR_TOKEN
User-Agent: curl/8.0.0
Accept: */*
```

**Curl Example:**
```bash
curl -X GET "http://localhost:3109/search?user_id=user123&session_id=session456&query=pizza&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response Example:**
```json
{
  "success": true,
  "message": "Found 1 memories",
  "data": {
    "user_id": "user123",
    "session_id": "session456",
    "composite_user_id": "user123_session456",
    "query": "pizza",
    "limit": 5,
    "results_count": 1,
    "memories": [
      {
        "id": "18c92972-98fd-45be-9743-89444089e85b",
        "memory": "Loves pizza",
        "hash": "c9e1d1fa098b22ff03ed58ec769cedd4",
        "createdAt": "2025-06-19T15:36:34.508Z",
        "score": 0.8783468679887432,
        "metadata": {"source": "chat"},
        "userId": "user123_session456"
      }
    ]
  },
  "timestamp": "2025-06-19T15:36:35.651Z"
}
```

---

### Get All Memories
**Endpoint:** `GET /all`
**Description:** Retrieves all memories for a specific user and session.

**Parameters:**
- `user_id` (string, required): User identifier
- `session_id` (string, required): Session identifier

**RFC 2616 HTTP Request:**
```http
GET /all?user_id=user123&session_id=session456 HTTP/1.1
Host: localhost:3109
Authorization: Bearer YOUR_TOKEN
User-Agent: curl/8.0.0
Accept: */*
```

**Curl Example:**
```bash
curl -X GET "http://localhost:3109/all?user_id=user123&session_id=session456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response Example:**
```json
{
  "success": true,
  "message": "Found 2 memories for user123/session456",
  "data": {
    "user_id": "user123",
    "session_id": "session456",
    "composite_user_id": "user123_session456",
    "memories_count": 2,
    "memories": [
      {
        "id": "18c92972-98fd-45be-9743-89444089e85b",
        "memory": "Loves pizza",
        "hash": "c9e1d1fa098b22ff03ed58ec769cedd4",
        "createdAt": "2025-06-19T15:36:34.508Z",
        "metadata": {"source": "chat"},
        "userId": "user123_session456"
      },
      {
        "id": "caab3fcc-bedf-4662-b24d-f2ce18629de2",
        "memory": "Enjoys Italian cuisine",
        "hash": "dd981da24ec387536303325c6928e8cf",
        "createdAt": "2025-06-19T15:36:42.084Z",
        "metadata": {},
        "userId": "user123_session456"
      }
    ]
  },
  "timestamp": "2025-06-19T15:36:42.340Z"
}
```

---

### Delete Specific Memory
**Endpoint:** `DELETE /delete`
**Description:** Deletes a specific memory by its ID.

**Parameters:**
- `memory_id` (string, required): ID of the memory to delete

**RFC 2616 HTTP Request:**
```http
DELETE /delete HTTP/1.1
Host: localhost:3109
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
Content-Length: 65

{
  "memory_id": "18c92972-98fd-45be-9743-89444089e85b"
}
```

**Curl Example:**
```bash
curl -X DELETE http://localhost:3109/delete \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "memory_id": "18c92972-98fd-45be-9743-89444089e85b"
  }'
```

**Response Example:**
```json
{
  "success": true,
  "message": "Successfully deleted memory 18c92972-98fd-45be-9743-89444089e85b",
  "data": {
    "memory_id": "18c92972-98fd-45be-9743-89444089e85b",
    "deleted": true,
    "result": {
      "message": "Memory deleted successfully!"
    }
  },
  "timestamp": "2025-06-19T15:36:41.995Z"
}
```

---

### Delete All Memories
**Endpoint:** `DELETE /deleteall`
**Description:** Deletes all memories for a specific user and session.

**Parameters:**
- `user_id` (string, required): User identifier
- `session_id` (string, required): Session identifier

**RFC 2616 HTTP Request:**
```http
DELETE /deleteall HTTP/1.1
Host: localhost:3109
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
Content-Length: 67

{
  "user_id": "user123",
  "session_id": "session456"
}
```

**Curl Example:**
```bash
curl -X DELETE http://localhost:3109/deleteall \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "user123",
    "session_id": "session456"
  }'
```

**Response Example:**
```json
{
  "success": true,
  "message": "Successfully deleted all memories for user123/session456",
  "data": {
    "user_id": "user123",
    "session_id": "session456",
    "composite_user_id": "user123_session456",
    "deleted_all": true,
    "result": {
      "message": "Memories deleted successfully!"
    }
  },
  "timestamp": "2025-06-19T15:38:54.084Z"
}
```

---

## 🔐 Authentication

All endpoints (except `/health`) require Bearer token authentication.

**RFC 2616 Header Format:**
```http
Authorization: Bearer YOUR_TOKEN
```

**RFC 2616 Example Request:**
```http
GET /all?user_id=user123&session_id=session456 HTTP/1.1
Host: localhost:3109
Authorization: Bearer mem0_secure_token_2024_dev
User-Agent: curl/8.0.0
Accept: */*
```

**Curl Example:**
```bash
curl -H "Authorization: Bearer mem0_secure_token_2024_dev" \
  "http://localhost:3109/all?user_id=user123&session_id=session456"
```

---

## ❌ Error Responses

All error responses follow a standardized format:

### Authentication Error (401)
```json
{
  "success": false,
  "error": "Access token required. Please provide a Bearer token in the Authorization header",
  "timestamp": "2025-06-19T15:39:17.764Z"
}
```

### Validation Error (400)
```json
{
  "success": false,
  "error": "Missing required fields",
  "validation_errors": [
    "Field 'user_id' is required",
    "Field 'session_id' is required"
  ],
  "timestamp": "2025-06-19T15:39:17.769Z"
}
```

### Not Found Error (404)
```json
{
  "success": false,
  "error": "Endpoint GET /nonexistent not found",
  "timestamp": "2025-06-19T15:39:17.751Z"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to process request",
  "timestamp": "2025-06-19T15:39:17.800Z",
  "details": "Additional error information"
}
```

---

## 🧪 Quick Testing Examples

### Complete Workflow Test

**1. Health Check (RFC 2616):**
```http
GET /health HTTP/1.1
Host: localhost:3109
```

**1. Health Check (Curl):**
```bash
curl http://localhost:3109/health
```

**2. Add Memory (RFC 2616):**
```http
POST /add HTTP/1.1
Host: localhost:3109
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
Content-Length: 180

{
  "user_id": "testuser",
  "session_id": "session001",
  "messages": [
    {"role": "user", "content": "I love testing APIs"},
    {"role": "assistant", "content": "Great! Testing ensures quality."}
  ]
}
```

**2. Add Memory (Curl):**
```bash
curl -X POST http://localhost:3109/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "testuser",
    "session_id": "session001",
    "messages": [
      {"role": "user", "content": "I love testing APIs"},
      {"role": "assistant", "content": "Great! Testing ensures quality."}
    ]
  }'
```

**3. Search Memories (RFC 2616):**
```http
GET /search?user_id=testuser&session_id=session001&query=testing&limit=5 HTTP/1.1
Host: localhost:3109
Authorization: Bearer YOUR_TOKEN
```

**3. Search Memories (Curl):**
```bash
curl "http://localhost:3109/search?user_id=testuser&session_id=session001&query=testing&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**4. Get All Memories (RFC 2616):**
```http
GET /all?user_id=testuser&session_id=session001 HTTP/1.1
Host: localhost:3109
Authorization: Bearer YOUR_TOKEN
```

**4. Get All Memories (Curl):**
```bash
curl "http://localhost:3109/all?user_id=testuser&session_id=session001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## ⚙️ Configuration

The application supports extensive configuration through environment variables. See `.env.example` for all available options:

### Key Configuration Options

- **LLM Provider**: Choose between OpenAI and OpenRouter
- **OpenRouter Fallback**: Automatic fallback to secondary model on failure
- **Queue Type**: Redis or in-memory processing
- **Vector Store**: Qdrant configuration
- **Authentication**: Bearer token setup
- **Logging**: Enable/disable detailed logging

## 🔧 Development

### Scripts
- **`npm start`**: Start the modular server
- **`npm run start:legacy`**: Start the legacy monolithic server
- **`npm run dev`**: Start in development mode
- **`npm test`**: Run comprehensive tests

### Key Benefits of Modular Architecture

1. **Maintainability**: Each module has a single responsibility
2. **Testability**: Individual components can be tested in isolation
3. **Scalability**: Easy to add new endpoints or modify existing ones
4. **Code Reuse**: Shared utilities and services
5. **Clear Dependencies**: Explicit imports show component relationships

## 🛡️ Error Handling

The application includes comprehensive error handling:

- **Input Validation**: Thorough validation of all inputs
- **Standardized Responses**: Consistent API response format
- **Logging**: Detailed logging for debugging and monitoring
- **Graceful Degradation**: Fallback mechanisms for LLM failures

## 🔄 Migration from Legacy

The legacy monolithic `api.cjs` file is still available but deprecated. The new modular structure provides:

- Better code organization
- Improved error handling
- Enhanced logging
- Easier testing and maintenance
- Standardized API responses

## 📝 License

MIT
